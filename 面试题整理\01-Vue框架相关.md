# Vue框架相关面试题

## Vue基础概念

### 1. Vue的双向绑定原理
- 考察对Vue响应式系统的理解
- 涉及数据劫持、观察者模式等概念

### 2. Vue的生命周期
- 各个生命周期钩子的执行时机
- 在不同阶段可以做什么操作

### 3. Vue中key的作用
- v-for为什么要使用key
- 如果使用index会有什么问题
- 与diff算法的关系

### 4. 讲讲Keep-Alive
- 组件缓存机制
- 使用场景和注意事项

## Vue响应式系统

### 5. Proxy相对于Object.defineProperty的提升
- Vue2与Vue3响应式实现的差异
- 各自的优缺点

### 6. vue2和vue3的差别
- 详细介绍响应式原理
- diff算法的改进
- Composition API vs Options API

### 7. 讲讲Diff算法
- 虚拟DOM的比较过程
- 时间复杂度优化
- key的作用机制

## Vue特殊机制

### 8. nextTick作用与使用场景
- DOM更新的异步机制
- 什么情况下会使用NextTick
- 纯前端有这个机制吗

### 9. 事件处理机制
- 事件冒泡、事件捕获、事件委托
- Vue中的事件修饰符

## 状态管理

### 10. vuex和pinia底层实现原理
- 状态管理的设计模式
- 响应式状态的实现
- 为什么选择某个状态管理工具

### 11. 发布订阅模式
- 在Vue中的应用
- 与观察者模式的区别

## Vue项目实践

### 12. Vue2向Vue3框架迁移
- 迁移过程中的注意事项
- 有没有自己的SOP（标准操作程序）
- 是否使用AI Agent辅助迁移

### 13. 全局状态管理工具选择
- 为什么使用某个全局状态管理工具
- 之前有对比过其他方案吗
- 不同方案的优缺点分析
