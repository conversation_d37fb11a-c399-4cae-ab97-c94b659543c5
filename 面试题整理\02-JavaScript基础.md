# JavaScript基础面试题

## 数据类型与变量

### 1. JS的基本数据类型
- 原始类型：number, string, boolean, null, undefined, symbol, bigint
- 引用类型：object

### 2. ES6新增了什么数据类型，ES2020新增的数据类型
- ES6: Symbol
- ES2020: BigInt
- 各自的使用场景

### 3. let和const
- 与var的区别
- 块级作用域的概念
- 暂时性死区

### 4. var未定义报什么错，let未定义报什么错，为什么错误不一样
- ReferenceError vs undefined
- 变量提升机制的差异

### 5. symbol概念及用途
- 唯一性保证
- 作为对象属性的应用
- 内置Symbol的使用

## 循环与迭代

### 6. for in 和 for of 的区别
- 遍历对象属性 vs 遍历可迭代对象
- 使用场景的差异

### 7. 实现一个可迭代类
- Iterator接口的实现
- Symbol.iterator的使用

## 对象与函数

### 8. 如何创建一个对象
- 字面量、构造函数、Object.create等方式
- 各种方式的特点

### 9. Js创建对象的方式
- 工厂模式、构造函数模式、原型模式等
- 各种模式的优缺点

### 10. New关键字的原理，具体做了什么操作
- 创建新对象、绑定this、执行构造函数、返回对象

### 11. 如何定义a，可满足a=1&a=2&a=3条件成立，可以使用proxy实现吗
- valueOf/toString重写
- Proxy拦截
- Object.defineProperty

## 闭包与作用域

### 12. 闭包特性
- 一定是函数套函数吗
- 除了可访问外层变量还有什么特性
- 项目中有哪些地方用到了闭包

### 13. 手写闭包
- 实际编程实现
- 应用场景展示

### 14. 闭包会造成什么问题？内存溢出会造成什么问题？
- 内存泄漏的风险
- 性能影响分析

## 异步编程

### 15. 事件循环
- 宏任务和微任务的执行顺序
- 如果当前同时存在宏任务和微任务是先清空微任务队列还是怎么样

### 16. 实现一个promise
- Promise/A+规范
- then、catch、finally的实现

### 17. 事件循环输出题
- 考察对异步执行顺序的理解
- 宏任务、微任务混合场景

## 数组操作

### 18. 会修改原数组和不会修改原数组的方法
- 修改原数组：push, pop, shift, unshift, splice, sort, reverse
- 不修改原数组：concat, slice, map, filter, reduce

### 19. unshift 作用
- 在数组开头添加元素
- 返回新数组长度

### 20. 数组扁平
- 递归实现
- flat方法使用
- 深度控制

### 21. 对象数组去重
- 根据特定属性去重
- 多种实现方案

## 字符串操作

### 22. 字符串指定位置插入字符
- slice + 拼接
- 正则替换等方法

### 23. substr()的调用输出
- 参数理解和边界情况
- 与substring、slice的区别

## 事件处理

### 24. evt.target和evt.currentTarget的区别
- 事件目标 vs 事件绑定元素
- 事件冒泡中的表现

### 25. 冒泡机制和捕获机制谁先谁后
- 捕获阶段 -> 目标阶段 -> 冒泡阶段
- addEventListener的第三个参数

### 26. 如何阻止事件冒泡
- stopPropagation()
- stopImmediatePropagation()

### 27. 如何阻止默认事件
- preventDefault()
- return false的区别

## 性能与优化

### 28. 防抖节流
- 概念理解和使用场景
- 手写防抖实现

### 29. JS的垃圾回收机制
- 标记清除、引用计数
- 内存管理最佳实践

### 30. 讲讲深拷贝和浅拷贝
- 实现方式对比
- JSON.stringify的局限性

## ES6+特性

### 31. 项目中有用到哪些es6特性
- 箭头函数、解构赋值、模板字符串
- Promise、async/await
- 模块化import/export

### 32. 函数传参是深拷贝还是浅拷贝
- 值传递 vs 引用传递
- 原始类型和引用类型的差异
