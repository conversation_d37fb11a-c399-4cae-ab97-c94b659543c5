# CSS样式相关面试题

## CSS基础概念

### 1. 有多少html标签可以表示列表
- `<ul>` 无序列表
- `<ol>` 有序列表  
- `<dl>` 定义列表
- `<menu>` 菜单列表

### 2. 伪类和伪元素的区别
- 伪类：选择元素的特定状态（:hover, :active, :focus等）
- 伪元素：创建虚拟元素（::before, ::after, ::first-line等）
- 语法差异：单冒号 vs 双冒号

### 3. nth::child是伪类还是伪元素
- nth-child是伪类（注意不是nth::child）
- 用于选择特定位置的子元素

## 文本样式

### 4. 如何设置文本属性的文字间距
- `letter-spacing`: 字符间距
- `word-spacing`: 单词间距
- 具体数值设置方法

### 5. 如何设置文本属性的垂直居中
- `line-height` 等于容器高度
- `vertical-align: middle`
- Flexbox: `align-items: center`
- Grid: `align-content: center`

### 6. 如何设置汉语段落的首行缩进
- `text-indent: 2em` 
- em单位相对于当前字体大小
- 中文段落的排版规范

## 布局相关

### 7. css代码题 实现指定布局方案
- 考察具体的布局实现能力
- 可能涉及Flexbox、Grid、定位等

### 8. 有哪些常见的布局属性，fixed一般用于什么场景
- 布局属性：static, relative, absolute, fixed, sticky
- fixed使用场景：
  - 固定导航栏
  - 回到顶部按钮
  - 固定侧边栏
  - 模态框遮罩

### 9. 如何实现全局遮罩
```css
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}
```

### 10. 实现各屏幕大小下可自适应的多宫格元素排列布局
- CSS Grid布局方案
- Flexbox + 媒体查询
- 响应式设计原则
```css
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-container {
    grid-template-columns: 1fr;
  }
}
```

## 高级CSS技巧

### 11. CSS选择器优先级
- 内联样式 > ID选择器 > 类选择器 > 标签选择器
- !important的使用和注意事项
- 权重计算规则

### 12. BFC（块级格式化上下文）
- 触发条件：overflow: hidden, float, position: absolute等
- 解决的问题：外边距塌陷、清除浮动等
- 实际应用场景

### 13. CSS预处理器
- Sass/Less/Stylus的特性
- 变量、嵌套、混入、函数等功能
- 编译过程和工具链

### 14. CSS-in-JS
- styled-components, emotion等方案
- 与传统CSS的对比
- 优缺点分析

## 响应式设计

### 15. 媒体查询
- 断点设置策略
- 移动优先 vs 桌面优先
- 常用媒体特性

### 16. 视口单位
- vw, vh, vmin, vmax的使用
- 与百分比的区别
- 兼容性考虑

### 17. 图片响应式
- srcset和sizes属性
- picture元素的使用
- 不同分辨率的适配

## CSS动画

### 18. transition vs animation
- 过渡 vs 关键帧动画
- 使用场景的差异
- 性能考虑

### 19. transform属性
- translate, rotate, scale, skew
- 3D变换
- 硬件加速优化

### 20. CSS性能优化
- 减少重排重绘
- 使用transform代替position变化
- will-change属性的使用
