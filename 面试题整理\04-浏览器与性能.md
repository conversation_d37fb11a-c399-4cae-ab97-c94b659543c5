# 浏览器与性能相关面试题

## 浏览器存储

### 1. 浏览器存储
- localStorage: 持久化存储，同源策略限制
- sessionStorage: 会话级存储，标签页关闭即清除
- cookie: 服务器通信，有过期时间和大小限制
- IndexedDB: 大容量客户端数据库
- WebSQL: 已废弃的关系型数据库

### 2. cookie中通常会携带什么信息
- 用户身份标识（session ID, token）
- 用户偏好设置（语言、主题等）
- 购物车信息
- 统计和分析数据
- 广告追踪信息

### 3. 如何设置cookie的过期时间
```javascript
// 设置过期时间
document.cookie = "name=value; expires=Thu, 18 Dec 2025 12:00:00 UTC";

// 设置最大存活时间（秒）
document.cookie = "name=value; max-age=3600";

// 服务端设置
Set-Cookie: name=value; expires=Thu, 18 Dec 2025 12:00:00 UTC
```

### 4. 页面通信
- 同源页面间通信：localStorage事件、BroadcastChannel
- 跨域通信：postMessage
- 页面数据联动 sessionStorage的应用

## 浏览器缓存

### 5. 浏览器缓存e-tag和last-modified的优先级
- ETag优先级高于Last-Modified
- ETag更精确，基于内容哈希
- Last-Modified基于时间，精度有限

### 6. 什么情况下会使用304？判断304是在本地还是CDN？
- 304 Not Modified：资源未修改，使用缓存
- 判断位置：可以在本地缓存、CDN、源服务器
- 通过请求头If-None-Match、If-Modified-Since触发

### 7. 301、302、304状态码
- 301: 永久重定向，搜索引擎会更新索引
- 302: 临时重定向，搜索引擎保持原URL
- 304: 资源未修改，使用缓存

### 8. 304状态码的请求头关键字
- `If-None-Match`: 配合ETag使用
- `If-Modified-Since`: 配合Last-Modified使用
- `Cache-Control`: 缓存控制指令

## 浏览器渲染

### 9. 浏览器访问到渲染
1. DNS解析：域名转IP地址
2. TCP连接：三次握手建立连接
3. HTTP请求：发送请求获取资源
4. 服务器响应：返回HTML文档
5. HTML解析：构建DOM树
6. CSS解析：构建CSSOM树
7. 渲染树构建：DOM + CSSOM
8. 布局计算：计算元素位置大小
9. 绘制：像素填充
10. 合成：图层合并显示

### 10. 重排（Reflow）和重绘（Repaint）
- 重排：元素几何属性变化，影响布局
- 重绘：元素样式变化，不影响布局
- 优化策略：批量操作、使用transform等

## 性能指标

### 11. Performance有哪些指标能够反映项目性能？
- **FCP (First Contentful Paint)**: 首次内容绘制
- **LCP (Largest Contentful Paint)**: 最大内容绘制
- **FID (First Input Delay)**: 首次输入延迟
- **CLS (Cumulative Layout Shift)**: 累积布局偏移
- **TTFB (Time to First Byte)**: 首字节时间
- **TTI (Time to Interactive)**: 可交互时间

### 12. 计算FCP的开始和结束点分别是什么？浏览器是怎么拿到这个时间的？
- 开始点：导航开始时间（navigationStart）
- 结束点：首次渲染任何内容的时间
- 获取方式：
  ```javascript
  // Performance Observer API
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.name === 'first-contentful-paint') {
        console.log('FCP:', entry.startTime);
      }
    }
  }).observe({entryTypes: ['paint']});
  ```

### 13. 性能优化策略
- 资源优化：压缩、合并、CDN
- 代码分割：按需加载、懒加载
- 缓存策略：浏览器缓存、服务端缓存
- 图片优化：格式选择、响应式图片
- 关键渲染路径优化

## 性能监控与调试

### 14. 有遇到过一个网页在用户的电脑上打开很慢，但在你的电脑上很快的情况吗，怎么处理的
- 网络环境差异：带宽、延迟
- 设备性能差异：CPU、内存、GPU
- 浏览器版本差异：兼容性问题
- 调试方法：
  - 远程调试工具
  - 性能监控平台
  - 网络限速测试
  - 设备模拟测试

### 15. 性能监控工具
- Chrome DevTools: Performance面板
- Lighthouse: 综合性能评估
- WebPageTest: 在线性能测试
- 真实用户监控（RUM）
- 合成监控（Synthetic Monitoring）

## 内存管理

### 16. 内存泄漏常见场景
- 全局变量未清理
- 事件监听器未移除
- 闭包引用未释放
- DOM节点引用未清理
- 定时器未清除

### 17. 内存泄漏检测
- Chrome DevTools Memory面板
- Heap Snapshot对比
- Performance面板内存曲线
- 代码审查和最佳实践

## Web Workers

### 18. 为什么要将拉取数据流放在Web Worker中？
- 避免阻塞主线程
- 提高用户界面响应性
- 处理大量数据计算
- 并行处理能力
- 使用场景：数据处理、图像处理、加密解密等
