# 网络与安全相关面试题

## HTTP协议

### 1. HTTP2.0相对HTTP1.1的提升
- **多路复用**: 单个连接并发处理多个请求
- **服务器推送**: 主动推送资源给客户端
- **头部压缩**: HPACK算法压缩HTTP头部
- **二进制分帧**: 更高效的数据传输格式
- **流优先级**: 重要资源优先传输
- **连接复用**: 减少TCP连接建立开销

### 2. TCP三次握手过程
1. **SYN**: 客户端发送连接请求，seq=x
2. **SYN+ACK**: 服务器确认并发送连接请求，seq=y, ack=x+1  
3. **ACK**: 客户端确认服务器连接请求，ack=y+1
- 目的：确保双方都有发送和接收能力

### 3. HTTP状态码详解
- **1xx**: 信息性状态码（100 Continue）
- **2xx**: 成功状态码（200 OK, 201 Created）
- **3xx**: 重定向状态码（301, 302, 304）
- **4xx**: 客户端错误（400, 401, 403, 404）
- **5xx**: 服务器错误（500, 502, 503）

## 跨域问题

### 4. 跨域，什么情况会出现跨域以及如何处理
**出现跨域的情况**：
- 协议不同：http vs https
- 域名不同：a.com vs b.com
- 端口不同：8080 vs 3000
- 子域名不同：www.a.com vs api.a.com

**解决方案**：
- **CORS**: 服务端设置Access-Control-Allow-Origin
- **JSONP**: 利用script标签不受同源策略限制
- **代理服务器**: 开发环境使用webpack-dev-server代理
- **postMessage**: 跨域通信API
- **document.domain**: 子域名跨域

### 5. 跨域想带cookie如何处理
```javascript
// 前端设置
fetch(url, {
  credentials: 'include'  // 携带cookie
});

// 或者XMLHttpRequest
xhr.withCredentials = true;
```

```http
// 服务端响应头
Access-Control-Allow-Credentials: true
Access-Control-Allow-Origin: https://specific-domain.com  // 不能是*
```

### 6. 什么请求会发预检
**简单请求条件**（不发预检）：
- 方法：GET, POST, HEAD
- 头部：Accept, Accept-Language, Content-Language, Content-Type
- Content-Type：text/plain, multipart/form-data, application/x-www-form-urlencoded

**复杂请求**（发预检）：
- 其他HTTP方法：PUT, DELETE, PATCH
- 自定义头部：Authorization, X-Requested-With
- Content-Type：application/json, application/xml

### 7. JSONP的原理
- 利用`<script>`标签不受同源策略限制
- 服务器返回JavaScript代码，调用预定义的回调函数
- 只支持GET请求
```javascript
// 客户端
function handleResponse(data) {
  console.log(data);
}

// 动态创建script标签
const script = document.createElement('script');
script.src = 'http://api.example.com/data?callback=handleResponse';
document.head.appendChild(script);

// 服务器返回
handleResponse({"name": "张三", "age": 25});
```

### 8. JSONP为什么只支持get请求
- `<script>`标签只能发起GET请求
- 无法设置请求方法、请求头、请求体
- 这是HTML标签的固有限制

## 安全防护

### 9. 如何防止csrf攻击
**CSRF（跨站请求伪造）防护**：
- **Token验证**: 在表单中添加随机token
- **Referer检查**: 验证请求来源
- **SameSite Cookie**: 限制cookie跨站发送
- **双重Cookie验证**: cookie和请求参数双重验证
```javascript
// Token验证示例
<form>
  <input type="hidden" name="csrf_token" value="random_token_value">
  <!-- 其他表单字段 -->
</form>
```

### 10. csrf攻击与预防
**攻击原理**：
- 用户登录受信任网站A，获得cookie
- 用户访问恶意网站B
- 网站B利用用户cookie向网站A发起请求

**预防措施**：
- 验证HTTP Referer字段
- 添加token验证
- 使用验证码
- 设置SameSite属性

### 11. xss攻击预防
**XSS（跨站脚本攻击）类型**：
- **存储型XSS**: 恶意脚本存储在服务器
- **反射型XSS**: 恶意脚本在URL参数中
- **DOM型XSS**: 通过DOM操作执行恶意脚本

**预防措施**：
- **输入验证**: 过滤特殊字符
- **输出编码**: HTML实体编码
- **CSP**: Content Security Policy
- **HttpOnly Cookie**: 防止JavaScript访问
```javascript
// 输出编码示例
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}
```

## 网络优化

### 12. CDN是什么？用处是？
**CDN（内容分发网络）**：
- **定义**: 分布式服务器网络，就近提供内容
- **用处**：
  - 加速静态资源加载
  - 减少源服务器压力
  - 提高可用性和容错性
  - 防御DDoS攻击
- **工作原理**: DNS解析到最近的边缘节点

### 13. A标签如何避免在新页面打开
```html
<!-- 默认在当前页面打开 -->
<a href="https://example.com">链接</a>

<!-- 避免新页面打开的方法 -->
<a href="https://example.com" target="_self">当前页面打开</a>

<!-- 如果设置了target="_blank"，可以移除 -->
<a href="https://example.com">移除target属性</a>
```

## WebSocket

### 14. 为什么要使用WebSocket，和直接使用Http异步请求有什么区别？
**WebSocket优势**：
- **全双工通信**: 服务器可主动推送数据
- **持久连接**: 减少连接建立开销
- **实时性**: 低延迟数据传输
- **协议开销小**: 无HTTP头部冗余

**与HTTP的区别**：
- HTTP：请求-响应模式，单向通信
- WebSocket：双向通信，持久连接
- 使用场景：实时聊天、股票行情、游戏等

```javascript
// WebSocket使用示例
const ws = new WebSocket('ws://localhost:8080');

ws.onopen = function(event) {
  console.log('连接已建立');
};

ws.onmessage = function(event) {
  console.log('收到消息:', event.data);
};

ws.send('Hello Server');
```

## 网络调试

### 15. 网络请求调试工具
- **Chrome DevTools**: Network面板
- **Postman**: API测试工具
- **Fiddler**: HTTP代理调试工具
- **Wireshark**: 网络协议分析器
- **curl**: 命令行HTTP客户端
