# 算法编程题面试题

## 数组操作

### 1. 数组扁平化
**题目**: 实现数组扁平化，支持多层嵌套
```javascript
// 递归实现
function flatten(arr) {
  const result = [];
  for (let item of arr) {
    if (Array.isArray(item)) {
      result.push(...flatten(item));
    } else {
      result.push(item);
    }
  }
  return result;
}

// 使用reduce
function flatten(arr) {
  return arr.reduce((acc, val) => 
    Array.isArray(val) ? acc.concat(flatten(val)) : acc.concat(val), []
  );
}

// ES2019 flat方法
arr.flat(Infinity);
```

### 2. 对象数组去重
**题目**: 根据对象的某个属性去重
```javascript
// 根据id去重
function uniqueById(arr) {
  const seen = new Set();
  return arr.filter(item => {
    if (seen.has(item.id)) {
      return false;
    }
    seen.add(item.id);
    return true;
  });
}

// 使用Map
function uniqueById(arr) {
  const map = new Map();
  return arr.filter(item => {
    if (map.has(item.id)) {
      return false;
    }
    map.set(item.id, true);
    return true;
  });
}
```

### 3. 类似数组拍平（需要注意边界情况处理）
**题目**: 实现数组扁平化，考虑各种边界情况
```javascript
function flattenArray(arr) {
  // 边界情况处理
  if (!Array.isArray(arr)) {
    throw new TypeError('参数必须是数组');
  }
  
  if (arr.length === 0) {
    return [];
  }
  
  const result = [];
  
  function flatten(item) {
    if (Array.isArray(item)) {
      for (let i = 0; i < item.length; i++) {
        flatten(item[i]);
      }
    } else {
      // 过滤掉undefined和null（可选）
      if (item !== undefined && item !== null) {
        result.push(item);
      }
    }
  }
  
  flatten(arr);
  return result;
}

// 测试边界情况
console.log(flattenArray([])); // []
console.log(flattenArray([1, [2, [3, [4]]]])); // [1, 2, 3, 4]
console.log(flattenArray([1, null, [2, undefined, [3]]])); // [1, 2, 3]
```

## 异步编程

### 4. 实现一个Promise
**题目**: 手写Promise实现，支持then、catch、finally
```javascript
class MyPromise {
  constructor(executor) {
    this.state = 'pending';
    this.value = undefined;
    this.reason = undefined;
    this.onFulfilledCallbacks = [];
    this.onRejectedCallbacks = [];
    
    const resolve = (value) => {
      if (this.state === 'pending') {
        this.state = 'fulfilled';
        this.value = value;
        this.onFulfilledCallbacks.forEach(fn => fn());
      }
    };
    
    const reject = (reason) => {
      if (this.state === 'pending') {
        this.state = 'rejected';
        this.reason = reason;
        this.onRejectedCallbacks.forEach(fn => fn());
      }
    };
    
    try {
      executor(resolve, reject);
    } catch (error) {
      reject(error);
    }
  }
  
  then(onFulfilled, onRejected) {
    return new MyPromise((resolve, reject) => {
      if (this.state === 'fulfilled') {
        setTimeout(() => {
          try {
            const result = onFulfilled(this.value);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      }
      
      if (this.state === 'rejected') {
        setTimeout(() => {
          try {
            const result = onRejected(this.reason);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      }
      
      if (this.state === 'pending') {
        this.onFulfilledCallbacks.push(() => {
          setTimeout(() => {
            try {
              const result = onFulfilled(this.value);
              resolve(result);
            } catch (error) {
              reject(error);
            }
          });
        });
        
        this.onRejectedCallbacks.push(() => {
          setTimeout(() => {
            try {
              const result = onRejected(this.reason);
              resolve(result);
            } catch (error) {
              reject(error);
            }
          });
        });
      }
    });
  }
}
```

### 5. 实现一个Promise并发控制（注意边界情况调试）
**题目**: 限制并发数量的Promise执行器
```javascript
class PromisePool {
  constructor(concurrency = 3) {
    this.concurrency = concurrency;
    this.running = 0;
    this.queue = [];
  }
  
  async add(promiseFunction) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        promiseFunction,
        resolve,
        reject
      });
      this.process();
    });
  }
  
  async process() {
    if (this.running >= this.concurrency || this.queue.length === 0) {
      return;
    }
    
    this.running++;
    const { promiseFunction, resolve, reject } = this.queue.shift();
    
    try {
      const result = await promiseFunction();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}

// 使用示例
const pool = new PromisePool(2);

// 边界情况测试
const tasks = [];
for (let i = 0; i < 10; i++) {
  tasks.push(
    pool.add(() => new Promise(resolve => {
      setTimeout(() => resolve(`Task ${i}`), Math.random() * 1000);
    }))
  );
}

Promise.all(tasks).then(results => {
  console.log('所有任务完成:', results);
});
```

## 数据结构

### 6. 如果不使用Map实现LRU缓存O(1)的时间复杂度
**题目**: 实现LRU（最近最少使用）缓存
```javascript
class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = new Map();
  }
  
  get(key) {
    if (this.cache.has(key)) {
      // 重新设置以更新顺序
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return -1;
  }
  
  put(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      // 删除最久未使用的项（Map的第一个项）
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}

// 不使用Map的实现（双向链表 + 哈希表）
class Node {
  constructor(key, value) {
    this.key = key;
    this.value = value;
    this.prev = null;
    this.next = null;
  }
}

class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = {}; // 哈希表
    
    // 创建虚拟头尾节点
    this.head = new Node(0, 0);
    this.tail = new Node(0, 0);
    this.head.next = this.tail;
    this.tail.prev = this.head;
  }
  
  addNode(node) {
    // 在头部添加节点
    node.prev = this.head;
    node.next = this.head.next;
    this.head.next.prev = node;
    this.head.next = node;
  }
  
  removeNode(node) {
    // 移除节点
    node.prev.next = node.next;
    node.next.prev = node.prev;
  }
  
  moveToHead(node) {
    // 移动到头部
    this.removeNode(node);
    this.addNode(node);
  }
  
  popTail() {
    // 移除尾部节点
    const lastNode = this.tail.prev;
    this.removeNode(lastNode);
    return lastNode;
  }
  
  get(key) {
    const node = this.cache[key];
    if (node) {
      this.moveToHead(node);
      return node.value;
    }
    return -1;
  }
  
  put(key, value) {
    const node = this.cache[key];
    
    if (node) {
      node.value = value;
      this.moveToHead(node);
    } else {
      const newNode = new Node(key, value);
      
      if (Object.keys(this.cache).length >= this.capacity) {
        const tail = this.popTail();
        delete this.cache[tail.key];
      }
      
      this.cache[key] = newNode;
      this.addNode(newNode);
    }
  }
}
```

## 特殊算法

### 7. 实现一个拼手气红包
**题目**: 实现红包金额随机分配算法
```javascript
function generateRedPackets(totalAmount, count) {
  if (count <= 0 || totalAmount <= 0) {
    throw new Error('参数无效');
  }
  
  if (count === 1) {
    return [totalAmount];
  }
  
  const result = [];
  let remaining = totalAmount;
  
  for (let i = 0; i < count - 1; i++) {
    // 确保每个人至少能分到0.01元
    const minAmount = 0.01;
    const maxAmount = (remaining - (count - i - 1) * minAmount) * 2 / (count - i);
    
    // 生成随机金额
    const amount = Math.random() * (maxAmount - minAmount) + minAmount;
    const roundedAmount = Math.round(amount * 100) / 100;
    
    result.push(roundedAmount);
    remaining -= roundedAmount;
  }
  
  // 最后一个人分剩余金额
  result.push(Math.round(remaining * 100) / 100);
  
  return result;
}

// 测试
console.log(generateRedPackets(100, 10)); // 分100元给10个人
```

## 字符串处理

### 8. 统计一段文字中出现最多的单词及次数（注意各种case的处理）
**题目**: 统计单词频率，处理各种边界情况
```javascript
function findMostFrequentWord(text) {
  // 边界情况处理
  if (!text || typeof text !== 'string') {
    return { word: '', count: 0 };
  }
  
  // 清理文本：转小写，移除标点符号，分割单词
  const words = text
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // 移除标点符号
    .split(/\s+/) // 按空白字符分割
    .filter(word => word.length > 0); // 过滤空字符串
  
  if (words.length === 0) {
    return { word: '', count: 0 };
  }
  
  // 统计词频
  const wordCount = {};
  for (const word of words) {
    wordCount[word] = (wordCount[word] || 0) + 1;
  }
  
  // 找出出现次数最多的单词
  let maxCount = 0;
  let mostFrequentWord = '';
  
  for (const [word, count] of Object.entries(wordCount)) {
    if (count > maxCount) {
      maxCount = count;
      mostFrequentWord = word;
    }
  }
  
  return { word: mostFrequentWord, count: maxCount };
}

// 测试各种情况
console.log(findMostFrequentWord("Hello world! Hello JavaScript. Hello world."));
// { word: 'hello', count: 3 }

console.log(findMostFrequentWord(""));
// { word: '', count: 0 }

console.log(findMostFrequentWord("   "));
// { word: '', count: 0 }
```

## 数据转换

### 9. 将包含node节点和tree节点的对象改为树型结构
**题目**: 扁平数据转树形结构
```javascript
function arrayToTree(flatArray, parentKey = 'parentId', idKey = 'id', childrenKey = 'children') {
  // 边界情况处理
  if (!Array.isArray(flatArray) || flatArray.length === 0) {
    return [];
  }
  
  const map = {};
  const roots = [];
  
  // 创建映射表
  flatArray.forEach(item => {
    map[item[idKey]] = { ...item, [childrenKey]: [] };
  });
  
  // 构建树形结构
  flatArray.forEach(item => {
    const node = map[item[idKey]];
    const parentId = item[parentKey];
    
    if (parentId && map[parentId]) {
      map[parentId][childrenKey].push(node);
    } else {
      roots.push(node);
    }
  });
  
  return roots;
}

// 测试数据
const flatData = [
  { id: 1, name: '根节点1', parentId: null },
  { id: 2, name: '子节点1-1', parentId: 1 },
  { id: 3, name: '子节点1-2', parentId: 1 },
  { id: 4, name: '子节点1-1-1', parentId: 2 },
  { id: 5, name: '根节点2', parentId: null }
];

console.log(JSON.stringify(arrayToTree(flatData), null, 2));
```

## 函数式编程

### 10. 函数柯里化
**题目**: 实现函数柯里化
```javascript
function curry(fn) {
  return function curried(...args) {
    if (args.length >= fn.length) {
      return fn.apply(this, args);
    } else {
      return function(...nextArgs) {
        return curried.apply(this, args.concat(nextArgs));
      };
    }
  };
}

// 使用示例
function add(a, b, c) {
  return a + b + c;
}

const curriedAdd = curry(add);
console.log(curriedAdd(1)(2)(3)); // 6
console.log(curriedAdd(1, 2)(3)); // 6
console.log(curriedAdd(1)(2, 3)); // 6
```

## 自定义Hook

### 11. 实现一个hook
**题目**: 实现一个自定义React Hook
```javascript
import { useState, useEffect } from 'react';

// 实现一个防抖Hook
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

// 实现一个本地存储Hook
function useLocalStorage(key, initialValue) {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error('Error reading localStorage:', error);
      return initialValue;
    }
  });
  
  const setValue = (value) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error setting localStorage:', error);
    }
  };
  
  return [storedValue, setValue];
}
```

## 数据验证

### 12. 指定规则，判断输入是否为合法json（内部需求）
**题目**: 实现JSON格式验证
```javascript
function isValidJSON(str) {
  // 基本类型检查
  if (typeof str !== 'string') {
    return false;
  }
  
  // 空字符串检查
  if (str.trim() === '') {
    return false;
  }
  
  try {
    const parsed = JSON.parse(str);
    // 确保解析结果是对象或数组
    return typeof parsed === 'object' && parsed !== null;
  } catch (error) {
    return false;
  }
}

// 更严格的JSON验证（自定义规则）
function isValidJSONStrict(str, rules = {}) {
  if (!isValidJSON(str)) {
    return false;
  }
  
  const parsed = JSON.parse(str);
  
  // 检查必需字段
  if (rules.requiredFields) {
    for (const field of rules.requiredFields) {
      if (!(field in parsed)) {
        return false;
      }
    }
  }
  
  // 检查字段类型
  if (rules.fieldTypes) {
    for (const [field, expectedType] of Object.entries(rules.fieldTypes)) {
      if (field in parsed && typeof parsed[field] !== expectedType) {
        return false;
      }
    }
  }
  
  return true;
}

// 测试
console.log(isValidJSON('{"name": "张三", "age": 25}')); // true
console.log(isValidJSON('invalid json')); // false
console.log(isValidJSONStrict('{"name": "张三", "age": 25}', {
  requiredFields: ['name', 'age'],
  fieldTypes: { name: 'string', age: 'number' }
})); // true
```
